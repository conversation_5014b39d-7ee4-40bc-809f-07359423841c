<?php
/**
 * Menu Responsive component
 *
 * @package Pimpampum_Tools
 */

namespace Pimpampum;

// If this file is called directly, abort.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Menu Responsive class
 */
class Menu_Responsive {
    
    /**
     * Initialize the component
     */
    public static function init() {
        add_action( 'cmb2_admin_init', array( __CLASS__, 'register_options' ) );
        
        $enabled = Tabs::get_option( 'main', 'menu-responsive' );
        

        if ( 'on' === $enabled ) {
            self::setup_hooks();
        }
    }
    
    /**
     * Set up hooks for the responsive menu
     */
    public static function setup_hooks() {
        add_action( 'wp_enqueue_scripts', array( __CLASS__, 'enqueue_scripts' ) );
    }
    
    /**
     * Enqueue scripts and styles
     */
    public static function enqueue_scripts() {
        // Enqueue our script after the theme's navigation script to override it
        ppp_enqueue_script( 'menu-responsive/menu-responsive.js', array('pimpampum-navigation'), 'ppp-menu-responsive' );
        ppp_enqueue_style( 'menu-responsive/menu-responsive.css', array(), 'ppp-menu-responsive-css' );
    }
    
    /**
     * Register component options
     */
    public static function register_options() {
        $tab = Tabs::get_tab( 'main' );
        
        if ( $tab ) {
            $tab->add_field( array(
                'name'        => __( 'Menú responsive', 'ppp' ),
                'description' => __( 'Afegeix menú per pantalles petites. Cal afegir button class="menu-toggle"...', 'ppp' ),
                'id'          => 'menu-responsive',
                'type'        => 'checkbox',
            ) );
        }
    }
}