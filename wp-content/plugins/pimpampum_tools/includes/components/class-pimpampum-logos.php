<?php
/**
 * Logos component
 *
 * @package Pimpampum_Tools
 */

namespace Pimpampum;

// If this file is called directly, abort.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Logos class for managing logos display
 */
class PimpampumLogos {
    
    /**
     * Initialize the component
     */
    public static function init() {
        add_action( 'cmb2_admin_init', array( __CLASS__, 'register_option' ) );
        
        $enabled = Tabs::get_option( 'main', 'logos' );
        
        if ( 'on' === $enabled ) {
            self::setup_hooks();
        }
    }
    
    /**
     * Set up hooks for the logos component
     */
    public static function setup_hooks() {
        add_action( 'wp_enqueue_scripts', array( __CLASS__, 'enqueue_scripts' ) );
        add_action( 'init', array( __CLASS__, 'register_post_type' ), 0 );
        add_action( 'cmb2_admin_init', array( __CLASS__, 'register_metabox' ) );
    }
    
    /**
     * Enqueue scripts and styles
     */
    public static function enqueue_scripts() {
        wp_enqueue_style(
            'logos-css',
            PPP_PLUGIN_URL . 'resources/logos/logos.css',
            array(),
            PPP_VERSION
        );
    }
    
    /**
     * Register the logos option in settings
     */
    public static function register_option() {
        $tab = Tabs::get_tab( 'main' );
        
        if ( $tab ) {
            $tab->add_field( array(
                'name'        => __( 'Logos', 'ppp' ),
                'description' => 'Secció per administrar el logos de la web amb enllaços opcionals',
                'id'          => 'logos',
                'type'        => 'checkbox',
            ) );
        }
    }
    
    /**
     * Display logos
     */
    public static function show() {
        Core::load_template( 'logos/logos.php' );
    }
    
    /**
     * Register logo post type
     */
    public static function register_post_type() {
        $labels = array(
            'name'                  => _x( 'Logo', 'Post Type General Name', 'ppp' ),
            'singular_name'         => _x( 'Logo', 'Post Type Singular Name', 'ppp' ),
            'menu_name'             => __( 'Logos', 'ppp' ),
            'name_admin_bar'        => __( 'Logo', 'ppp' ),
        );
        
        $args = array(
            'label'                 => __( 'Logo', 'ppp' ),
            'description'           => __( 'Post Type Description', 'ppp' ),
            'labels'                => $labels,
            'supports'              => array(
                'title',
                'thumbnail',
            ),
            'taxonomies'            => array(),
            'hierarchical'          => true,
            'public'                => true,
            'show_ui'               => true,
            'show_in_menu'          => true,
            'menu_position'         => 5,
            'menu_icon'             => 'dashicons-image-filter',
            'show_in_admin_bar'     => true,
            'show_in_nav_menus'     => true,
            'can_export'            => true,
            'has_archive'           => true,
            'exclude_from_search'   => false,
            'publicly_queryable'    => true,
            'capability_type'       => 'post',
            'yarpp_support'         => true,
            'show_in_rest'          => true,
        );
        
        register_post_type( 'logo', $args );
    }
    
    /**
     * Register metabox for logos
     */
    public static function register_metabox() {
        $cmb_logos = new_cmb2_box( array(
            'id'           => PIMPAMPUM_PREFIX . 'metaboxlogos',
            'title'        => esc_html__( 'Logos metadata', 'ppp' ),
            'object_types' => array( 'logo' ),
        ) );
        
        Core::add_cmb2_field( $cmb_logos, 'Link', 'link', 'text_url', 'Opcional' );
    }
}