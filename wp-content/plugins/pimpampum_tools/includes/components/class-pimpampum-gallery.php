<?php
/**
 * Gallery component
 *
 * @package Pimpampum_Tools
 */

namespace Pimpampum;

// If this file is called directly, abort.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Gallery class for image galleries with PhotoSwipe integration
 */
class Gallery {
    
    /**
     * Initialize the component
     */
    public static function init() {
        add_action( 'cmb2_admin_init', array( __CLASS__, 'register_options' ) );
        
        $sliders = Tabs::get_option( 'main', 'sliders' );
        
        if ( 'on' === $sliders ) {
            self::setup_hooks();
        }
    }
    
    /**
     * Set up hooks for the gallery component
     */
    public static function setup_hooks() {
        add_action( 'wp_enqueue_scripts', array( __CLASS__, 'enqueue_scripts' ) );
        add_action( 'cmb2_admin_init', array( __CLASS__, 'register_metabox' ) );
        add_shortcode( 'ppp-gallery', array( __CLASS__, 'shortcode_handler' ) );
        
        // Add custom image sizes
       //600_cropped', 600, 386, true );
    }
    
    /**
     * Enqueue scripts and styles
     */
    public static function enqueue_scripts() {
        wp_enqueue_script( 
            'photoswipe-ui', 
            PPP_PLUGIN_URL . 'vendor/photoswipe/jquery.photoswipe-global.js', 
            array( 'jquery' ), 
            PPP_VERSION, 
            true 
        );
        
        wp_enqueue_script( 
            'photoswipe-code', 
            PPP_PLUGIN_URL . 'resources/gallery/gallery.js', 
            array( 'jquery' ), 
            PPP_VERSION, 
            true 
        );
        
        wp_enqueue_style( 
            'photoswipe-css', 
            PPP_PLUGIN_URL . 'vendor/photoswipe/photoswipe.css', 
            array(), 
            PPP_VERSION 
        );
        
        wp_enqueue_style( 
            'photoswipe-skin', 
            PPP_PLUGIN_URL . 'vendor/photoswipe/default-skin/default-skin.css', 
            array(), 
            PPP_VERSION 
        );
    }
    
    /**
     * Register gallery options
     */
    public static function register_options() {
        $tab = Tabs::get_tab( 'main' );

        if ( $tab ) {
            $tab->add_field( array(
                'name'        => 'Galeries d\'imatge',
                'description' => 'Activa les galeries d\'imatge per a posts i pàgines. Després a la plantilla afegir [ppp-gallery]',
                'id'          => 'sliders',
                'type'        => 'checkbox',
            ) );

            $tab->add_field( array(
                'name'        => 'Tipus de contingut per galeries',
                'description' => 'Escriure els tipus de contingut on volem galeries d\'imatge separats per comes, per exemple post,page',
                'id'          => 'sliders_post_types',
                'type'        => 'text',
                'default'     => 'post,page',
            ) );
        }
    }
    
    /**
     * Display the gallery
     *
     * @param bool $single Whether this is a single gallery display.
     */
    public static function display( $single = false ) {
        Core::load_template( 'gallery/slides.php' );
    }
    
    /**
     * Register the gallery metabox
     */
    public static function register_metabox() {
        $post_types_setting = Tabs::get_option( 'main', 'sliders_post_types', 'post,page' );
        $post_types = array_map( 'trim', explode( ',', $post_types_setting ) );

        if ( empty( $post_types ) || empty( $post_types_setting ) ) {
            return;
        }

        $cmb = new_cmb2_box( array(
            'id'           => PIMPAMPUM_PREFIX . 'ppp-metabox',
            'title'        => esc_html__( 'Pimpampum tools', 'ppp' ),
            'object_types' => $post_types,
        ) );

        $cmb->add_field( array(
            'name' => esc_html__( 'Description', 'ppp' ),
            'id'   => PIMPAMPUM_PREFIX . 'description',
            'type' => 'text',
        ) );

        $cmb->add_field( array(
            'name'         => esc_html__( 'Images', 'ppp' ),
            'desc'         => esc_html__( 'Upload or add multiple images/attachments.', 'ppp' ),
            'id'           => PIMPAMPUM_PREFIX . 'image_list',
            'type'         => 'file_list',
            'preview_size' => array( 100, 100 ),
        ) );
    }
    
    /**
     * Gallery shortcode handler
     *
     * @param array $atts Shortcode attributes.
     * @return string Shortcode output.
     */
    public static function shortcode_handler( $atts ) {
        ob_start();
        self::display( true );
        return ob_get_clean();
    }
}