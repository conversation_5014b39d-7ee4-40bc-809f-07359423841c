<?php
/**
 * Plugin Name: Pimpampum Tools
 * Description: A collection of tools for WordPress websites
 * Version: 3.0.0
 * Author: Pimpampum
 * Author URI: http://www.pimpampum.net
 * License: GPL2
 * Text Domain: ppp
 * Domain Path: /languages
 *
 * @package Pimpampum_Tools
 */

// If this file is called directly, abort.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

// Define plugin constants
define( 'PPP_VERSION', '3.0.0' );
define( 'PPP_PLUGIN_DIR', plugin_dir_path( __FILE__ ) );
define( 'PPP_PLUGIN_URL', plugin_dir_url( __FILE__ ) );
define( 'PPP_INCLUDES_DIR', PPP_PLUGIN_DIR . 'includes/' );
define( 'PPP_TEMPLATES_DIR', PPP_PLUGIN_DIR . 'ppp-templates/' );
define( 'PPP_RESOURCES_DIR', PPP_PLUGIN_DIR . 'resources/' );
define( 'PPP_ASSETS_URL', PPP_PLUGIN_URL . 'resources/' );

// Load configuration
require_once( PPP_PLUGIN_DIR . 'ppp-config.php' );

/**
 * The main plugin class
 */
final class Pimpampum_Tools {

    /**
     * Instance of this class
     *
     * @var Pimpampum_Tools
     */
    private static $instance;

    /**
     * Get the singleton instance of this class
     *
     * @return Pimpampum_Tools
     */
    public static function get_instance() {
        if ( null === self::$instance ) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Constructor
     */
    private function __construct() {
        $this->load_dependencies();
        $this->define_hooks();
    }

    /**
     * Load all dependencies
     */
    private function load_dependencies() {
        // Choose whether to load the refactored files or the original ones
        if ( defined( 'PPP_USE_REFACTORED' ) && PPP_USE_REFACTORED ) {
            $this->load_refactored_files();
            // We no longer load both sets of files when PPP_USE_REFACTORED is true
        } else {
            $this->load_original_files();
        }
        
        // Add a debug comment to the footer for troubleshooting
        add_action('wp_footer', function() {
            echo '<!-- PPP Tools: ' . (defined('PPP_USE_REFACTORED') && PPP_USE_REFACTORED ? 'Using refactored code only' : 'Using legacy code') . ' -->';
        }, 1);
    }
    
    /**
     * Load the refactored files
     */
    private function load_refactored_files() {
        // Core class
        require_once PPP_INCLUDES_DIR . 'class-pimpampum-core.php';
        
        // Compatibility layer (must be loaded after Core)
        require_once PPP_INCLUDES_DIR . 'class-pimpampum-compatibility.php';
        
        // Components
        require_once PPP_INCLUDES_DIR . 'components/class-pimpampum-tabs.php';
        require_once PPP_INCLUDES_DIR . 'components/class-pimpampum-menu-responsive.php';
        require_once PPP_INCLUDES_DIR . 'components/class-pimpampum-events.php';
        require_once PPP_INCLUDES_DIR . 'components/class-pimpampum-gallery.php';
        require_once PPP_INCLUDES_DIR . 'components/class-pimpampum-slideshow.php';
        require_once PPP_INCLUDES_DIR . 'components/class-pimpampum-cookies.php';
        require_once PPP_INCLUDES_DIR . 'components/class-pimpampum-social.php';
        require_once PPP_INCLUDES_DIR . 'components/class-pimpampum-share.php';
        require_once PPP_INCLUDES_DIR . 'components/class-pimpampum-icon-widget.php';
        require_once PPP_INCLUDES_DIR . 'components/class-pimpampum-logos.php';
        require_once PPP_INCLUDES_DIR . 'components/class-pimpampum-widgets.php';
        require_once PPP_INCLUDES_DIR . 'components/class-pimpampum-map.php';
        require_once PPP_INCLUDES_DIR . 'components/class-pimpampum-404.php';

        
        // General functions
        require_once PPP_INCLUDES_DIR . 'pimpampum-general.php';
    }
    
   
    /**
     * Register all hooks
     */
    private function define_hooks() {
        // Register activation and deactivation hooks
        register_activation_hook( __FILE__, array( $this, 'activate' ) );
        register_deactivation_hook( __FILE__, array( $this, 'deactivate' ) );
        
        // Initialize components on plugins_loaded
        add_action( 'plugins_loaded', array( $this, 'init_components' ) );
    }
    
    /**
     * Initialize all components
     */
    public function init_components() {
        if ( defined( 'PPP_USE_REFACTORED' ) && PPP_USE_REFACTORED ) {
            // Initialize refactored components
            \Pimpampum\Core::init();
            \Pimpampum\Tabs::init();
            \Pimpampum\Menu_Responsive::init();
            \Pimpampum\Events::init();
            \Pimpampum\Gallery::init();
            \Pimpampum\Slideshow::init();
            \Pimpampum\Cookies::init();
            \Pimpampum\Social::init();
            \Pimpampum\Share::init();
            \Pimpampum\Icon_Widget::init();
            \Pimpampum\PimpampumLogos::init();
            \Pimpampum\Widgets::init();
            \Pimpampum\Map::init();
            \Pimpampum\Custom_404::init();
        }
        // Original files auto-initialize
    }

    /**
     * Plugin activation
     */
    public function activate() {
        // Activation tasks
    }

    /**
     * Plugin deactivation
     */
    public function deactivate() {
        // Deactivation tasks
    }
}

// Initialize the plugin
Pimpampum_Tools::get_instance();