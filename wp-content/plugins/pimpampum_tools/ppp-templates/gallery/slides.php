  <?php use Pimpampum\Core as ppp; ?>


  <div id="js-gallery">
<?php

  $images = get_post_meta( get_the_ID(), PIMPAMPUM_PREFIX."image_list", true );

  // Debug: Show what we found
  if ( current_user_can( 'manage_options' ) ) {
      echo '<!-- Gallery Debug: Post ID: ' . get_the_ID() . ', Images found: ' . ( $images ? count( $images ) : 'none' ) . ' -->';
      if ( $images ) {
          echo '<!-- Images: ' . print_r( $images, true ) . ' -->';
      }
  }

  if(!$images) return;
  	foreach($images as $id=>$image){
  $data=wp_get_attachment_metadata($id);
  $meta = ppp::get_attachment($id);

  $caption=$meta['title']; //igual podria ser el caption
  $medium=wp_get_attachment_image_src($id,'thumbnail');
  $large=wp_get_attachment_image_src($id,'large');
  //data-original-src-width="2000" data-original-src-height="2000"


?>

<a class="slide" href="<?php echo $large[0]?>">
    <img src="<?php echo $medium[0]?>"

    alt="<?php echo $caption?>">
</a>


<?php
}
?>
</div>
