<?php

/** versió on es veu una imatge gran i les altres petites **/
use Pimpampum\Core as ppp; ?>


<div class="image-gallery">

<?php

$images = get_post_meta( get_the_ID(), PIMPAMPUM_PREFIX."image_list", true );
if(!$images) return;
$image=wp_get_attachment_image_src(key($images),'large');

?>

<div class="image-big">
  <img src="<?php echo $image[0]?>">
</div>

<div class="image-thumbnails" id="js-gallery-">
<?php
$i=0;
foreach($images as $id=>$image){
  $data=wp_get_attachment_metadata($id);
  $meta = ppp::get_attachment($id);
  $caption=$meta['title']; //igual podria ser el caption
  $medium=wp_get_attachment_image_src($id,'thumbnail');
  $large=wp_get_attachment_image_src($id,'large');
  //data-original-src-width="2000" data-original-src-height="2000"
  ?>

  <a class="slide <?php if($i==0) echo "selected"?>" data-img="<?php echo $large[0]?>" href="<?php echo $large[0]?>">
    <img src="<?php echo $medium[0]?>"

    alt="<?php echo $caption?>">
  </a>




<?php
}
?>
</div>

</div>

<script>
(function ($) {
    jQuery(document).ready(function(){

      $('.image-thumbnails a').on('click',function(ev){
        var img_src=$(this).data('img');
        $('.image-big img').attr('src',img_src);
        $('.image-thumbnails a ').removeClass("selected");
        $(this).addClass("selected");
        ev.preventDefault();
      });

      $('.image-big').on('click',function(ev){

      });
  });
}( jQuery ));

</script>
