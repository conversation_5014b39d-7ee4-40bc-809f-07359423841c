console.log('🍔 MENU RESPONSIVE SCRIPT LOADED! 🍔');
console.log('Menu Responsive: Script starting...');

const navBar = document.getElementById("site-navigation");

// Check if navigation element exists
if (!navBar) {
    console.error('Menu Responsive: site-navigation element not found');
    console.log('Available elements with navigation in ID:', document.querySelectorAll('[id*="navigation"]'));
    return;
}

console.log('Menu Responsive: Found site-navigation element:', navBar);

// Check if theme already has a menu toggle button
const existingToggle = navBar.querySelector('.menu-toggle');
let hamburger;

if (existingToggle) {
    // Use existing button but replace its content with hamburger icon
    existingToggle.innerHTML = '<span class="bar"></span><span class="bar"></span><span class="bar"></span>';
    existingToggle.classList.add('hamburger');

    // Remove all existing event listeners by cloning the element
    const newToggle = existingToggle.cloneNode(true);
    existingToggle.parentNode.replaceChild(newToggle, existingToggle);
    hamburger = newToggle;

    console.log('Menu Responsive: Using existing menu-toggle button');
} else {
    // Create new hamburger button
    var html='<button class="hamburger menu-toggle" type="button"><span class="bar"></span><span class="bar"></span><span class="bar"></span></button>';
    navBar.insertAdjacentHTML('beforeend', html);
    hamburger = navBar.querySelector(".hamburger");

    console.log('Menu Responsive: Created new hamburger button');
}

if (!hamburger) {
    console.warn('Menu Responsive: hamburger element not found');
    return;
}

// Find the menu - try multiple selectors
const navMenu = navBar.querySelector("ul") ||
               document.querySelector("#primary-menu") ||
               document.querySelector(".menu") ||
               document.querySelector("#menu-principal");

if (!navMenu) {
    console.warn('Menu Responsive: navigation menu not found');
    return;
}

console.log('Menu Responsive: Found menu element:', navMenu);

const navLinks = navBar.querySelectorAll(".menu-item a, li a");

console.log('Menu Responsive: Adding click event to hamburger:', hamburger);
hamburger.addEventListener("click", mobileMenu);

// Test if the event listener was added
hamburger.addEventListener("click", function() {
    console.log('Menu Responsive: CLICK DETECTED!');
});

function mobileMenu(e) {
    console.log('Menu Responsive: mobileMenu function called!', e);
    e.preventDefault();
    e.stopPropagation();

    const isActive = hamburger.classList.contains("active");
    console.log('Menu Responsive: Current active state:', isActive);

    if (isActive) {
        // Close menu
        hamburger.classList.remove("active");
        navMenu.classList.remove("active");
        navBar.classList.remove("toggled");
        hamburger.setAttribute('aria-expanded', 'false');
        console.log('Menu Responsive: Menu closed');
    } else {
        // Open menu
        hamburger.classList.add("active");
        navMenu.classList.add("active");
        navBar.classList.add("toggled");
        hamburger.setAttribute('aria-expanded', 'true');
        console.log('Menu Responsive: Menu opened');
    }
}

if (navLinks.length > 0) {
    navLinks.forEach(link => link.addEventListener("click", closeMenu));
    console.log('Menu Responsive: Added click handlers to', navLinks.length, 'menu links');
}

function closeMenu() {
    hamburger.classList.remove("active");
    navMenu.classList.remove("active");
    navBar.classList.remove("toggled");
    hamburger.setAttribute('aria-expanded', 'false');
    console.log('Menu Responsive: Menu closed via link click');
}

// Close menu when clicking outside
document.addEventListener('click', function(event) {
    const isClickInside = navBar.contains(event.target);

    if (!isClickInside && hamburger.classList.contains('active')) {
        closeMenu();
    }
});

console.log('Menu Responsive: Initialization complete');
