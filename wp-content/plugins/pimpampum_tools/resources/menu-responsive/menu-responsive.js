const navBar = document.getElementById("site-navigation");

// Check if navigation element exists
if (!navBar) {
    console.warn('Menu Responsive: site-navigation element not found');
    return;
}

// Check if theme already has a menu toggle button
const existingToggle = navBar.querySelector('.menu-toggle');
let hamburger;

if (existingToggle) {
    // Use existing button but replace its content with hamburger icon
    existingToggle.innerHTML = '<span class="bar"></span><span class="bar"></span><span class="bar"></span>';
    existingToggle.classList.add('hamburger');
    hamburger = existingToggle;
} else {
    // Create new hamburger button
    var html='<div class="hamburger"><span class="bar"></span><span class="bar"></span><span class="bar"></span></div>';
    navBar.innerHTML = navBar.innerHTML + html;
    hamburger = navBar.querySelector(".hamburger");
}

if (!hamburger) {
    console.warn('Menu Responsive: hamburger element not found');
    return;
}

// Find the menu - try multiple selectors
const navMenu = navBar.querySelector("ul") ||
               document.querySelector("#primary-menu") ||
               document.querySelector(".menu") ||
               document.querySelector("#menu-principal");

const navLinks = navBar.querySelectorAll(".menu-item a, li a");

// Remove theme's default click handler if it exists
if (existingToggle) {
    const newToggle = existingToggle.cloneNode(true);
    existingToggle.parentNode.replaceChild(newToggle, existingToggle);
    hamburger = newToggle;
}

hamburger.addEventListener("click", mobileMenu);

function mobileMenu(e) {
    e.preventDefault();
    e.stopPropagation();

    hamburger.classList.toggle("active");

    if (navMenu) {
        navMenu.classList.toggle("active");
    }

    // Also toggle the theme's class for compatibility
    navBar.classList.toggle("toggled");
}

if (navLinks.length > 0) {
    navLinks.forEach(link => link.addEventListener("click", closeMenu));
}

function closeMenu() {
    hamburger.classList.remove("active");
    if (navMenu) {
        navMenu.classList.remove("active");
    }
    navBar.classList.remove("toggled");
}
