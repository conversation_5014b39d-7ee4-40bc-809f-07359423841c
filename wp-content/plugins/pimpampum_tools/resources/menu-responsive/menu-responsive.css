@media (max-width: 767px) {

/* Hide theme's default menu toggle text and style the button */
.menu-toggle {
    background: none !important;
    border: none !important;
    padding: 5px !important;
    font-size: 0 !important;
    color: transparent !important;
    box-shadow: none !important;
    outline: none !important;
}

/* Ensure menu is hidden by default on mobile */
@media (max-width: 767px) {
    #primary-menu,
    #menu-principal,
    .menu,
    nav ul,
    #site-navigation ul {
        display: none;
    }
}

/* Mobile menu positioning - hidden by default */
#primary-menu,
#menu-principal,
.menu,
nav ul,
#site-navigation ul {
position: fixed;
left: -100%;
width: 100%;
height: 100%;
top: 0;
background-color: #fff;
text-align: left;
padding-top: 60px;
padding-left: 5%;
z-index: 100;
transition: left 0.3s ease-in-out;
list-style: none;
margin: 0;
display: block !important; /* Override the display: none */
}

/* Active states - support both our .active class and theme's .toggled class */
#primary-menu.active,
#menu-principal.active,
.menu.active,
nav ul.active,
#site-navigation.toggled ul,
#site-navigation ul.active {
left: 0 !important;
right: 0;
bottom: 0;
}

/* Menu items in mobile */
#primary-menu li,
#menu-principal li,
.menu li,
nav ul li {
display: block;
margin: 10px 0;
}

#primary-menu li a,
#menu-principal li a,
.menu li a,
nav ul li a {
display: block;
padding: 10px 0;
font-size: 18px;
color: #333;
text-decoration: none;
}

.hamburger {
display: block !important;
cursor: pointer;
position: absolute;
right: 20px;
top: 20px;
z-index: 101;
background: none;
border: none;
padding: 5px;
width: auto;
height: auto;
/* Debug: Add a visible background to see the button */
background-color: rgba(255, 0, 0, 0.1) !important;
min-width: 35px;
min-height: 35px;
}

.bar {
display: block;
width: 25px;
height: 3px;
margin: 5px auto;
-webkit-transition: all 0.3s ease-in-out;
transition: all 0.3s ease-in-out;
background-color: #101010;
border-radius: 2px;
}

.hamburger.active {
position: fixed;
right: 20px;
top: 20px;
}

.hamburger.active .bar:nth-child(1) {
transform: translateY(8px) rotate(45deg);
}

.hamburger.active .bar:nth-child(2) {
opacity: 0;
}

.hamburger.active .bar:nth-child(3) {
transform: translateY(-8px) rotate(-45deg);
}


}

@media (min-width: 768px) {

.hamburger {
display: none;
}

.menu,
#menu-principal,
nav ul {
display: flex;
justify-content: space-between;
align-items: center;
position: static;
width: auto;
height: auto;
background-color: transparent;
padding: 0;
}

#menu-principal {
margin-left: 5rem;
}

.menu-item,
.menu-item a {
font-size: 1.6rem;
font-weight: 400;
color: #475569;
text-decoration: none;
}

.menu-item:hover,
.menu-item a:hover {
color: #482ff7;
}

/*.nav-logo {
font-size: 2.1rem;
font-weight: 500;
color: #482ff7;
}*/
}
